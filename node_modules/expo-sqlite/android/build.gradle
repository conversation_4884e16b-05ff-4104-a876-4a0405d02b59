import org.apache.tools.ant.taskdefs.condition.Os

plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

String toPlatformIndependentPath(File path) {
  def result = path.toString()
  if (Os.isFamily(Os.FAMILY_WINDOWS)) {
    result = result.replace(File.separatorChar, '/' as char)
  }
  return result
}

ext {
  USE_SQLCIPHER = findProperty('expo.sqlite.useSQLCipher') == 'true'
  USE_LIBSQL = findProperty('expo.sqlite.useLibSQL') == 'true'
}
def SQLITE3_SRC_DIR = new File("${projectDir}/../vendor/sqlite3")
if (ext.USE_SQLCIPHER) {
  SQLITE3_SRC_DIR = new File("${projectDir}/../vendor/sqlcipher")
}

def getSQLiteBuildFlags() {
  def buildFlags = '-DSQLITE_ENABLE_BYTECODE_VTAB=1 -DSQLITE_TEMP_STORE=2'
  buildFlags <<= ' -DSQLITE_ENABLE_SESSION=1 -DSQLITE_ENABLE_PREUPDATE_HOOK=1'
  if (findProperty('expo.sqlite.enableFTS') != 'false') {
    buildFlags <<= ' -DSQLITE_ENABLE_FTS4=1 -DSQLITE_ENABLE_FTS3_PARENTHESIS=1 -DSQLITE_ENABLE_FTS5=1'
  }
  if (ext.USE_SQLCIPHER) {
    buildFlags <<= ' -DSQLITE_HAS_CODEC=1 -DSQLITE_EXTRA_INIT=sqlcipher_extra_init -DSQLITE_EXTRA_SHUTDOWN=sqlcipher_extra_shutdown -DSQLCIPHER_CRYPTO_OPENSSL'
  }
  def customBuildFlags = findProperty('expo.sqlite.customBuildFlags') ?: ''
  if (customBuildFlags != '') {
    buildFlags <<= " ${customBuildFlags}"
  }
  logger.info("SQLite build flags: ${buildFlags}")
  return buildFlags
}

def reactNativeArchitectures() {
  def value = project.getProperties().get("reactNativeArchitectures")
  return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

group = 'host.exp.exponent'
version = '15.2.14'

android {
  namespace "expo.modules.sqlite"
  defaultConfig {
    versionCode 18
    versionName "15.2.14"
    buildConfigField "boolean", "USE_LIBSQL", project.ext.USE_LIBSQL.toString()

    externalNativeBuild {
      cmake {
        abiFilters (*reactNativeArchitectures())
        arguments "-DANDROID_STL=c++_shared",
          "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON",
          "-DSQLITE3_SRC_DIR=${toPlatformIndependentPath(SQLITE3_SRC_DIR)}",
          "-DSQLITE_BUILDFLAGS=${getSQLiteBuildFlags()}",
          "-DUSE_SQLCIPHER=${project.ext.USE_SQLCIPHER}",
          "-DUSE_LIBSQL=${project.ext.USE_LIBSQL}"
      }
    }
  }
  externalNativeBuild {
    cmake {
      path "CMakeLists.txt"
    }
  }
  buildFeatures {
    buildConfig true
    prefab true
  }
  packagingOptions {
    excludes += [
      "**/libc++_shared.so",
      "**/libfbjni.so",
    ]
  }
}

dependencies {
  if (project.ext.USE_SQLCIPHER) {
    compileOnly 'io.github.ronickg:openssl:3.3.2'
  }
  compileOnly 'com.facebook.fbjni:fbjni:0.3.0'
}
