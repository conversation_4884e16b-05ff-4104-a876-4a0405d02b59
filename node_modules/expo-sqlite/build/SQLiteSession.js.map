{"version": 3, "file": "SQLiteSession.js", "sourceRoot": "", "sources": ["../src/SQLiteSession.ts"], "names": [], "mappings": "AAKA;;;GAGG;AACH,MAAM,OAAO,aAAa;IAEL;IACA;IAFnB,YACmB,cAA8B,EAC9B,aAA4B;QAD5B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,kBAAa,GAAb,aAAa,CAAe;IAC5C,CAAC;IAEJ,0BAA0B;IAE1B;;;;OAIG;IACI,WAAW,CAAC,KAAoB;QACrC,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,OAAgB;QACjC,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED;;;OAGG;IACI,UAAU;QACf,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG;IACI,oBAAoB;QACzB,OAAO,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACtE,CAAC;IAED;;;OAGG;IACI,4BAA4B;QACjC,OAAO,IAAI,CAAC,aAAa,CAAC,4BAA4B,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC9E,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,SAAoB;QAC7C,OAAO,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IAChF,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,SAAoB;QAC9C,OAAO,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IACjF,CAAC;IAED,YAAY;IAEZ,yBAAyB;IAEzB;;;;;;;OAOG;IACI,UAAU,CAAC,KAAoB;QACpC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;;OAOG;IACI,UAAU,CAAC,OAAgB;QAChC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;;OAMG;IACI,SAAS;QACd,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB;QACxB,OAAO,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACrE,CAAC;IAED;;;;;OAKG;IACI,2BAA2B;QAChC,OAAO,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC7E,CAAC;IAED;;;;;;;OAOG;IACI,kBAAkB,CAAC,SAAoB;QAC5C,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IACxE,CAAC;IAED;;;;;;;OAOG;IACI,mBAAmB,CAAC,SAAoB;QAC7C,OAAO,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IAChF,CAAC;CAGF", "sourcesContent": ["import { type NativeDatabase } from './NativeDatabase';\nimport { NativeSession, type Changeset } from './NativeSession';\n\nexport { type Changeset };\n\n/**\n * A class that represents an instance of the SQLite session extension.\n * @see [Session Extension](https://www.sqlite.org/sessionintro.html)\n */\nexport class SQLiteSession {\n  constructor(\n    private readonly nativeDatabase: NativeDatabase,\n    private readonly nativeSession: NativeSession\n  ) {}\n\n  //#region Asynchronous API\n\n  /**\n   * Attach a table to the session asynchronously.\n   * @see [`sqlite3session_attach`](https://www.sqlite.org/session/sqlite3session_attach.html)\n   * @param table The table to attach. If `null`, all tables are attached.\n   */\n  public attachAsync(table: string | null): Promise<void> {\n    return this.nativeSession.attachAsync(this.nativeDatabase, table);\n  }\n\n  /**\n   * Enable or disable the session asynchronously.\n   * @see [`sqlite3session_enable`](https://www.sqlite.org/session/sqlite3session_enable.html)\n   * @param enabled Whether to enable or disable the session.\n   */\n  public enableAsync(enabled: boolean): Promise<void> {\n    return this.nativeSession.enableAsync(this.nativeDatabase, enabled);\n  }\n\n  /**\n   * Close the session asynchronously.\n   * @see [`sqlite3session_delete`](https://www.sqlite.org/session/sqlite3session_delete.html)\n   */\n  public closeAsync(): Promise<void> {\n    return this.nativeSession.closeAsync(this.nativeDatabase);\n  }\n\n  /**\n   * Create a changeset asynchronously.\n   * @see [`sqlite3session_changeset`](https://www.sqlite.org/session/sqlite3session_changeset.html)\n   */\n  public createChangesetAsync(): Promise<Changeset> {\n    return this.nativeSession.createChangesetAsync(this.nativeDatabase);\n  }\n\n  /**\n   * Create an inverted changeset asynchronously.\n   * This is a shorthand for [`createChangesetAsync()`](#createchangesetasync) + [`invertChangesetAsync()`](#invertchangesetasyncchangeset).\n   */\n  public createInvertedChangesetAsync(): Promise<Changeset> {\n    return this.nativeSession.createInvertedChangesetAsync(this.nativeDatabase);\n  }\n\n  /**\n   * Apply a changeset asynchronously.\n   * @see [`sqlite3changeset_apply`](https://www.sqlite.org/session/sqlite3changeset_apply.html)\n   * @param changeset The changeset to apply.\n   */\n  public applyChangesetAsync(changeset: Changeset): Promise<void> {\n    return this.nativeSession.applyChangesetAsync(this.nativeDatabase, changeset);\n  }\n\n  /**\n   * Invert a changeset asynchronously.\n   * @see [`sqlite3changeset_invert`](https://www.sqlite.org/session/sqlite3changeset_invert.html)\n   * @param changeset The changeset to invert.\n   */\n  public invertChangesetAsync(changeset: Changeset): Promise<Changeset> {\n    return this.nativeSession.invertChangesetAsync(this.nativeDatabase, changeset);\n  }\n\n  //#endregion\n\n  //#region Synchronous API\n\n  /**\n   * Attach a table to the session synchronously.\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @param table The table to attach.\n   * @see [`sqlite3session_attach`](https://www.sqlite.org/session/sqlite3session_attach.html)\n   */\n  public attachSync(table: string | null): void {\n    this.nativeSession.attachSync(this.nativeDatabase, table);\n  }\n\n  /**\n   * Enable or disable the session synchronously.\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @param enabled Whether to enable or disable the session.\n   * @see [`sqlite3session_enable`](https://www.sqlite.org/session/sqlite3session_enable.html)\n   */\n  public enableSync(enabled: boolean): void {\n    this.nativeSession.enableSync(this.nativeDatabase, enabled);\n  }\n\n  /**\n   * Close the session synchronously.\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @see [`sqlite3session_delete`](https://www.sqlite.org/session/sqlite3session_delete.html)\n   */\n  public closeSync(): void {\n    this.nativeSession.closeSync(this.nativeDatabase);\n  }\n\n  /**\n   * Create a changeset synchronously.\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @see [`sqlite3session_changeset`](https://www.sqlite.org/session/sqlite3session_changeset.html)\n   */\n  public createChangesetSync(): Changeset {\n    return this.nativeSession.createChangesetSync(this.nativeDatabase);\n  }\n\n  /**\n   * Create an inverted changeset synchronously.\n   * This is a shorthand for [`createChangesetSync()`](#createchangesetsync) + [`invertChangesetSync()`](#invertchangesetsyncchangeset).\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   */\n  public createInvertedChangesetSync(): Changeset {\n    return this.nativeSession.createInvertedChangesetSync(this.nativeDatabase);\n  }\n\n  /**\n   * Apply a changeset synchronously.\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @param changeset The changeset to apply.\n   * @see [`sqlite3changeset_apply`](https://www.sqlite.org/session/sqlite3changeset_apply.html)\n   */\n  public applyChangesetSync(changeset: Changeset): void {\n    this.nativeSession.applyChangesetSync(this.nativeDatabase, changeset);\n  }\n\n  /**\n   * Invert a changeset synchronously.\n   *\n   * > **Note:** Running heavy tasks with this function can block the JavaScript thread and affect performance.\n   *\n   * @param changeset The changeset to invert.\n   * @see [`sqlite3changeset_invert`](https://www.sqlite.org/session/sqlite3changeset_invert.html)\n   */\n  public invertChangesetSync(changeset: Changeset): Changeset {\n    return this.nativeSession.invertChangesetSync(this.nativeDatabase, changeset);\n  }\n\n  //#endregion\n}\n"]}