{"version": 3, "file": "NativeDatabase.js", "sourceRoot": "", "sources": ["../src/NativeDatabase.ts"], "names": [], "mappings": "AAmFA;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,OAA0B;IAC3D,MAAM,EAAE,aAAa,EAAE,GAAG,WAAW,EAAE,GAAG,OAAO,CAAC;IAClD,MAAM,MAAM,GAAyB;QACnC,GAAG,WAAW;KACf,CAAC;IACF,IAAI,aAAa,EAAE,CAAC;QAClB,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;YACpB,SAAS,EAAE,aAAa,CAAC,GAAG;YAC5B,eAAe,EAAE,aAAa,CAAC,SAAS;YACxC,gBAAgB,EAAE,aAAa,CAAC,UAAU;SAC3C,CAAC,CAAC;IACL,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import { NativeSession } from './NativeSession';\nimport { NativeStatement } from './NativeStatement';\n\n/**\n * A class that represents an instance of the SQLite database.\n */\nexport declare class NativeDatabase {\n  constructor(databasePath: string, options?: SQLiteOpenOptions, serializedData?: Uint8Array);\n\n  //#region Asynchronous API\n\n  public initAsync(): Promise<void>;\n  public isInTransactionAsync(): Promise<boolean>;\n  public closeAsync(): Promise<void>;\n  public execAsync(source: string): Promise<void>;\n  public serializeAsync(databaseName: string): Promise<Uint8Array>;\n  public prepareAsync(nativeStatement: NativeStatement, source: string): Promise<NativeStatement>;\n  public createSessionAsync(nativeSession: NativeSession, dbName: string): Promise<NativeSession>;\n\n  //#endregion\n\n  //#region Synchronous API\n\n  public initSync(): void;\n  public isInTransactionSync(): boolean;\n  public closeSync(): void;\n  public execSync(source: string): void;\n  public serializeSync(databaseName: string): Uint8Array;\n  public prepareSync(nativeStatement: NativeStatement, source: string): NativeStatement;\n  public createSessionSync(nativeSession: NativeSession, dbName: string): NativeSession;\n\n  //#endregion\n\n  public syncLibSQL(): Promise<void>;\n}\n\n/**\n * Options for opening a database.\n */\nexport interface SQLiteOpenOptions {\n  /**\n   * Whether to call the [`sqlite3_update_hook()`](https://www.sqlite.org/c3ref/update_hook.html) function and enable the `onDatabaseChange` events. You can later subscribe to the change events by [`addDatabaseChangeListener`](#sqliteadddatabasechangelistenerlistener).\n   * @default false\n   */\n  enableChangeListener?: boolean;\n\n  /**\n   * Whether to create new connection even if connection with the same database name exists in cache.\n   * @default false\n   */\n  useNewConnection?: boolean;\n\n  /**\n   * Finalized unclosed statements automatically when the database is closed.\n   * @default true\n   * @hidden\n   */\n  finalizeUnusedStatementsBeforeClosing?: boolean;\n\n  /**\n   * Options for libSQL integration.\n   */\n  libSQLOptions?: {\n    /** The URL of the libSQL server. */\n    url: string;\n\n    /** The auth token for the libSQL server. */\n    authToken: string;\n\n    /**\n     * Whether to use remote-only without syncing to local database.\n     * @default false\n     */\n    remoteOnly?: boolean;\n  };\n}\n\ntype FlattenedOpenOptions = Omit<SQLiteOpenOptions, 'libSQLOptions'> & {\n  libSQLUrl?: string;\n  libSQLAuthToken?: string;\n  libSQLRemoteOnly?: boolean;\n};\n\n/**\n * Flattens the SQLiteOpenOptions that are passed to the native module.\n */\nexport function flattenOpenOptions(options: SQLiteOpenOptions): FlattenedOpenOptions {\n  const { libSQLOptions, ...restOptions } = options;\n  const result: FlattenedOpenOptions = {\n    ...restOptions,\n  };\n  if (libSQLOptions) {\n    Object.assign(result, {\n      libSQLUrl: libSQLOptions.url,\n      libSQLAuthToken: libSQLOptions.authToken,\n      libSQLRemoteOnly: libSQLOptions.remoteOnly,\n    });\n  }\n  return result;\n}\n"]}