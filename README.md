# Finance Tracker

A beautiful, minimal, and functional personal finance tracking app built with React Native and Expo.

## Features

✨ **Core Features**
- 📱 **Transaction Management** - Add, edit, and delete income/expense transactions
- 🏷️ **Smart Categories** - Organize transactions with predefined categories
- 📊 **Analytics Dashboard** - Visual insights with charts and trends
- 💰 **Balance Tracking** - Monitor your financial health
- 🔍 **Search & Filter** - Find transactions quickly

✨ **Design**
- 🎨 **Minimal & Luxurious** - Clean, modern interface
- 📱 **iPhone Optimized** - Perfect for iOS devices
- 🌙 **Beautiful Charts** - Pie charts and line graphs for analytics
- 🎯 **Intuitive Navigation** - Tab-based navigation

✨ **Technical**
- 📱 **React Native + Expo** - Cross-platform development
- 🗄️ **SQLite Database** - Local data storage for privacy
- 📊 **Chart Kit** - Beautiful data visualizations
- 🎨 **Material Design** - React Native Paper components

## Screenshots

The app features three main screens:
1. **Transactions** - View and manage all your financial transactions
2. **Analytics** - Visualize spending patterns and trends
3. **Settings** - App information and features

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- Expo CLI
- iOS Simulator or physical iPhone

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd FinanceTracker
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on your device**
   - Scan the QR code with Expo Go app (iOS/Android)
   - Or press `i` to open iOS simulator
   - Or press `a` to open Android emulator

## Usage

### Adding Transactions
1. Tap the **+** button on the Transactions screen
2. Choose **Income** or **Expense**
3. Enter the amount and description
4. Select a category
5. Set the date
6. Tap **Add Transaction**

### Viewing Analytics
- Switch to the **Analytics** tab
- View monthly overview with income, expenses, and balance
- See 6-month trends
- Explore category breakdowns with pie charts
- Toggle between income and expense analytics

### Categories
The app comes with predefined categories:

**Income Categories:**
- Salary, Freelance, Investment, Gift

**Expense Categories:**
- Food & Dining, Transportation, Shopping, Entertainment
- Bills & Utilities, Healthcare, Education, Travel

## Architecture

```
src/
├── components/          # Reusable UI components
│   ├── TransactionCard.tsx
│   ├── StatsCard.tsx
│   ├── CategoryPicker.tsx
│   └── AddTransactionModal.tsx
├── screens/            # Main app screens
│   ├── TransactionsScreen.tsx
│   ├── AnalyticsScreen.tsx
│   └── SettingsScreen.tsx
├── database/           # SQLite database layer
│   └── database.ts
├── context/            # React Context for state management
│   └── DatabaseContext.tsx
├── types/              # TypeScript type definitions
│   └── index.ts
└── theme/              # App theming and colors
    └── theme.ts
```

## Database Schema

### Tables
- **transactions** - Stores all financial transactions
- **categories** - Predefined and custom categories

### Key Features
- Foreign key relationships
- Automatic timestamps
- Data validation
- Optimized queries for analytics

## Privacy & Security

- 🔒 **Local Storage** - All data stays on your device
- 🚫 **No Cloud Sync** - No external servers or accounts required
- 🔐 **SQLite Encryption** - Database stored locally and securely

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with [Expo](https://expo.dev/)
- UI components from [React Native Paper](https://reactnativepaper.com/)
- Charts powered by [React Native Chart Kit](https://github.com/indiespirit/react-native-chart-kit)
- Icons from [Material Icons](https://fonts.google.com/icons)

---

**Finance Tracker** - Take control of your finances with style! 💰✨
