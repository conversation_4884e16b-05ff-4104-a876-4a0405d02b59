{"name": "financetracker", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "expo": "~53.0.20", "expo-router": "^5.1.4", "expo-sqlite": "^15.2.14", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.6.0", "react-native-svg": "^15.12.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}