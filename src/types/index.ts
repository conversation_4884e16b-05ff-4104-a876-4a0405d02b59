export interface Transaction {
  id: number;
  amount: number;
  description: string;
  category_id: number;
  type: 'income' | 'expense';
  date: string; // ISO string
  created_at: string; // ISO string
  updated_at: string; // ISO string
}

export interface Category {
  id: number;
  name: string;
  icon: string;
  color: string;
  type: 'income' | 'expense' | 'both';
  created_at: string; // ISO string
}

export interface TransactionWithCategory extends Transaction {
  category_name: string;
  category_icon: string;
  category_color: string;
}

export interface MonthlyStats {
  month: string; // YYYY-MM format
  total_income: number;
  total_expenses: number;
  net_amount: number;
  transaction_count: number;
}

export interface CategoryStats {
  category_id: number;
  category_name: string;
  category_icon: string;
  category_color: string;
  total_amount: number;
  transaction_count: number;
  percentage: number;
}

export interface DashboardData {
  current_balance: number;
  monthly_income: number;
  monthly_expenses: number;
  recent_transactions: TransactionWithCategory[];
  top_categories: CategoryStats[];
}
