import React, { createContext, useContext, useEffect, useState } from "react";
import { database } from "../database/database";
import { Transaction, Category, TransactionWithCategory } from "../types";

interface DatabaseContextType {
  // Transactions
  transactions: TransactionWithCategory[];
  addTransaction: (
    transaction: Omit<Transaction, "id" | "created_at" | "updated_at">
  ) => Promise<void>;
  updateTransaction: (
    id: number,
    transaction: Partial<Omit<Transaction, "id" | "created_at">>
  ) => Promise<void>;
  deleteTransaction: (id: number) => Promise<void>;
  refreshTransactions: () => Promise<void>;

  // Categories
  categories: Category[];
  getCategories: (type?: "income" | "expense") => Promise<Category[]>;
  refreshCategories: () => Promise<void>;

  // Analytics
  getMonthlyStats: (
    year: number,
    month: number
  ) => Promise<{ income: number; expenses: number; balance: number }>;
  getCategoryStats: (
    year: number,
    month: number,
    type: "income" | "expense"
  ) => Promise<any[]>;

  // Loading states
  isLoading: boolean;
}

const DatabaseContext = createContext<DatabaseContextType | undefined>(
  undefined
);

export const DatabaseProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [transactions, setTransactions] = useState<TransactionWithCategory[]>(
    []
  );
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const refreshTransactions = async () => {
    try {
      const data = await database.getTransactions();
      setTransactions(data);
    } catch (error) {
      console.error("Error fetching transactions:", error);
    }
  };

  const refreshCategories = async () => {
    try {
      const data = await database.getCategories();
      setCategories(data);
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const addTransaction = async (
    transaction: Omit<Transaction, "id" | "created_at" | "updated_at">
  ) => {
    try {
      await database.addTransaction(transaction);
      await refreshTransactions();
    } catch (error) {
      console.error("Error adding transaction:", error);
      throw error;
    }
  };

  const updateTransaction = async (
    id: number,
    transaction: Partial<Omit<Transaction, "id" | "created_at">>
  ) => {
    try {
      await database.updateTransaction(id, transaction);
      await refreshTransactions();
    } catch (error) {
      console.error("Error updating transaction:", error);
      throw error;
    }
  };

  const deleteTransaction = async (id: number) => {
    try {
      await database.deleteTransaction(id);
      await refreshTransactions();
    } catch (error) {
      console.error("Error deleting transaction:", error);
      throw error;
    }
  };

  const getCategories = async (type?: "income" | "expense") => {
    try {
      return await database.getCategories(type);
    } catch (error) {
      console.error("Error fetching categories:", error);
      throw error;
    }
  };

  const getMonthlyStats = async (year: number, month: number) => {
    try {
      return await database.getMonthlyStats(year, month);
    } catch (error) {
      console.error("Error fetching monthly stats:", error);
      throw error;
    }
  };

  const getCategoryStats = async (
    year: number,
    month: number,
    type: "income" | "expense"
  ) => {
    try {
      return await database.getCategoryStats(year, month, type);
    } catch (error) {
      console.error("Error fetching category stats:", error);
      throw error;
    }
  };

  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);
      try {
        await database.initialize();
        await Promise.all([refreshTransactions(), refreshCategories()]);
      } catch (error) {
        console.error("Error initializing data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, []);

  const value: DatabaseContextType = {
    transactions,
    addTransaction,
    updateTransaction,
    deleteTransaction,
    refreshTransactions,
    categories,
    getCategories,
    refreshCategories,
    getMonthlyStats,
    getCategoryStats,
    isLoading,
  };

  return (
    <DatabaseContext.Provider value={value}>
      {children}
    </DatabaseContext.Provider>
  );
};

export const useDatabase = () => {
  const context = useContext(DatabaseContext);
  if (context === undefined) {
    throw new Error("useDatabase must be used within a DatabaseProvider");
  }
  return context;
};
