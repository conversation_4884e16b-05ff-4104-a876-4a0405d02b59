import { database } from '../database/database';

export const seedDemoData = async () => {
  try {
    // Sample transactions for the current month
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    const demoTransactions = [
      // Income transactions
      {
        amount: 5000,
        description: 'Monthly Salary',
        category_id: 1, // Salary
        type: 'income' as const,
        date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`,
      },
      {
        amount: 800,
        description: 'Freelance Project',
        category_id: 2, // Freelance
        type: 'income' as const,
        date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-15`,
      },
      
      // Expense transactions
      {
        amount: 120,
        description: 'Grocery Shopping',
        category_id: 5, // Food & Dining
        type: 'expense' as const,
        date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-02`,
      },
      {
        amount: 45,
        description: 'Gas Station',
        category_id: 6, // Transportation
        type: 'expense' as const,
        date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-03`,
      },
      {
        amount: 89,
        description: 'Restaurant Dinner',
        category_id: 5, // Food & Dining
        type: 'expense' as const,
        date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-05`,
      },
      {
        amount: 250,
        description: 'New Clothes',
        category_id: 7, // Shopping
        type: 'expense' as const,
        date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-07`,
      },
      {
        amount: 15,
        description: 'Movie Tickets',
        category_id: 8, // Entertainment
        type: 'expense' as const,
        date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-10`,
      },
      {
        amount: 180,
        description: 'Electricity Bill',
        category_id: 9, // Bills & Utilities
        type: 'expense' as const,
        date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-12`,
      },
      {
        amount: 75,
        description: 'Coffee & Lunch',
        category_id: 5, // Food & Dining
        type: 'expense' as const,
        date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-14`,
      },
      {
        amount: 60,
        description: 'Uber Rides',
        category_id: 6, // Transportation
        type: 'expense' as const,
        date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-16`,
      },
    ];

    // Add demo transactions
    for (const transaction of demoTransactions) {
      await database.addTransaction(transaction);
    }

    console.log('Demo data seeded successfully!');
  } catch (error) {
    console.error('Error seeding demo data:', error);
  }
};
