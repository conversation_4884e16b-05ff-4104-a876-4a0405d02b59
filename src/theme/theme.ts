import { MD3LightTheme as DefaultTheme } from 'react-native-paper';

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#6366F1', // Indigo
    primaryContainer: '#E0E7FF',
    secondary: '#10B981', // Emerald
    secondaryContainer: '#D1FAE5',
    tertiary: '#F59E0B', // Amber
    tertiaryContainer: '#FEF3C7',
    surface: '#FFFFFF',
    surfaceVariant: '#F8FAFC',
    background: '#F8FAFC',
    error: '#EF4444',
    errorContainer: '#FEE2E2',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#1E1B4B',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#064E3B',
    onTertiary: '#FFFFFF',
    onTertiaryContainer: '#92400E',
    onSurface: '#1F2937',
    onSurfaceVariant: '#6B7280',
    onBackground: '#1F2937',
    onError: '#FFFFFF',
    onErrorContainer: '#7F1D1D',
    outline: '#D1D5DB',
    outlineVariant: '#E5E7EB',
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#374151',
    inverseOnSurface: '#F9FAFB',
    inversePrimary: '#A5B4FC',
    elevation: {
      level0: 'transparent',
      level1: '#FFFFFF',
      level2: '#F8FAFC',
      level3: '#F1F5F9',
      level4: '#E2E8F0',
      level5: '#CBD5E1',
    },
  },
  roundness: 12,
};

export const colors = {
  income: '#10B981', // Emerald
  expense: '#EF4444', // Red
  neutral: '#6B7280', // Gray
  success: '#10B981',
  warning: '#F59E0B',
  info: '#3B82F6',
  light: '#F8FAFC',
  dark: '#1F2937',
};
