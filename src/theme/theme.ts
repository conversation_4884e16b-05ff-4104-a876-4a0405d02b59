import { MD3LightTheme as DefaultTheme } from 'react-native-paper';

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#3d3aedff', // Modern purple
    primaryContainer: '#EDE9FE',
    secondary: '#059669', // Modern emerald
    secondaryContainer: '#D1FAE5',
    tertiary: '#DC2626', // Modern red
    tertiaryContainer: '#FEE2E2',
    surface: '#FFFFFF',
    surfaceVariant: '#F9FAFB',
    background: '#F9FAFB',
    error: '#DC2626',
    errorContainer: '#FEE2E2',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#3C1A78',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#064E3B',
    onTertiary: '#FFFFFF',
    onTertiaryContainer: '#7F1D1D',
    onSurface: '#111827',
    onSurfaceVariant: '#6B7280',
    onBackground: '#111827',
    onError: '#FFFFFF',
    onErrorContainer: '#7F1D1D',
    outline: '#D1D5DB',
    outlineVariant: '#E5E7EB',
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#374151',
    inverseOnSurface: '#F9FAFB',
    inversePrimary: '#A78BFA',
    elevation: {
      level0: 'transparent',
      level1: '#FFFFFF',
      level2: '#FEFEFE',
      level3: '#F8FAFC',
      level4: '#F1F5F9',
      level5: '#E2E8F0',
    },
  },
  roundness: 16,
};

export const colors = {
  income: '#059669', // Modern emerald
  expense: '#DC2626', // Modern red
  neutral: '#6B7280', // Gray
  success: '#059669',
  warning: '#D97706',
  info: '#2563EB',
  light: '#F9FAFB',
  dark: '#111827',

  // Gradient colors for modern look
  gradients: {
    primary: ['#7C3AED', '#A855F7'],
    income: ['#059669', '#10B981'],
    expense: ['#DC2626', '#EF4444'],
    neutral: ['#6B7280', '#9CA3AF'],
  },
};
