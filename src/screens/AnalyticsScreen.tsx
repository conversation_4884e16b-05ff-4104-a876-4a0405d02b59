import React, { useState, useEffect } from "react";
import { View, StyleSheet, ScrollView, Dimensions } from "react-native";
import { Text, Card, SegmentedButtons, useTheme } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";
import { <PERSON><PERSON><PERSON>, LineChart } from "react-native-chart-kit";
import { MaterialIcons } from "@expo/vector-icons";
import { useDatabase } from "../context/DatabaseContext";
import { colors } from "../theme/theme";

const screenWidth = Dimensions.get("window").width;

export default function AnalyticsScreen() {
  const theme = useTheme();
  const { transactions, getMonthlyStats, getCategoryStats } = useDatabase();

  const [selectedType, setSelectedType] = useState<"income" | "expense">(
    "expense"
  );
  const [monthlyStats, setMonthlyStats] = useState({
    income: 0,
    expenses: 0,
    balance: 0,
  });
  const [categoryStats, setCategoryStats] = useState<any[]>([]);
  const [monthlyTrends, setMonthlyTrends] = useState<any[]>([]);

  useEffect(() => {
    loadAnalyticsData();
  }, [transactions, selectedType]);

  const loadAnalyticsData = async () => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    try {
      // Load current month stats
      const stats = await getMonthlyStats(currentYear, currentMonth);
      setMonthlyStats(stats);

      // Load category stats for current month
      const catStats = await getCategoryStats(
        currentYear,
        currentMonth,
        selectedType
      );
      setCategoryStats(catStats);

      // Load last 6 months trends
      const trends = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date(currentYear, currentMonth - 1 - i, 1);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;

        const monthStats = await getMonthlyStats(year, month);
        trends.push({
          month: date.toLocaleDateString("en-US", { month: "short" }),
          income: monthStats.income,
          expenses: monthStats.expenses,
        });
      }
      setMonthlyTrends(trends);
    } catch (error) {
      console.error("Error loading analytics data:", error);
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const preparePieChartData = () => {
    if (categoryStats.length === 0) return [];

    return categoryStats.slice(0, 6).map((cat, index) => ({
      name: cat.category_name,
      amount: cat.total_amount,
      color: cat.category_color || `hsl(${index * 60}, 70%, 50%)`,
      legendFontColor: theme.colors.onSurface,
      legendFontSize: 12,
    }));
  };

  const prepareLineChartData = () => {
    if (monthlyTrends.length === 0) {
      return {
        labels: [],
        datasets: [{ data: [] }],
      };
    }

    return {
      labels: monthlyTrends.map((trend) => trend.month),
      datasets: [
        {
          data: monthlyTrends.map((trend) =>
            selectedType === "income" ? trend.income : trend.expenses
          ),
          color: () =>
            selectedType === "income" ? colors.income : colors.expense,
          strokeWidth: 3,
        },
      ],
    };
  };

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    propsForBackgroundLines: {
      stroke: theme.colors.onSurfaceVariant,
      strokeDasharray: "1",
      strokeWidth: 0.2,
    },
    color: () => theme.colors.primary,
    labelColor: () => theme.colors.onSurface,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: "4",
      strokeWidth: "1",
      stroke: selectedType === "income" ? colors.income : colors.expense,
    },
  };

  const pieData = preparePieChartData();
  const lineData = prepareLineChartData();

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      edges={["left", "right"]}
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <Text
            variant="headlineSmall"
            style={[styles.title, { color: theme.colors.onSurface }]}
          >
            Analytics
          </Text>

          {/* Monthly Overview */}
          <Card style={styles.card} mode="outlined">
            <Card.Content>
              <Text variant="titleMedium" style={styles.cardTitle}>
                This Month Overview
              </Text>
              <View style={styles.overviewStats}>
                <View style={styles.statItem}>
                  <MaterialIcons
                    name="trending-up"
                    size={24}
                    color={colors.income}
                  />
                  <Text variant="bodySmall" style={styles.statLabel}>
                    Income
                  </Text>
                  <Text
                    variant="titleMedium"
                    style={[styles.statValue, { color: colors.income }]}
                  >
                    {formatAmount(monthlyStats.income)}
                  </Text>
                </View>
                <View style={styles.statItem}>
                  <MaterialIcons
                    name="trending-down"
                    size={24}
                    color={colors.expense}
                  />
                  <Text variant="bodySmall" style={styles.statLabel}>
                    Expenses
                  </Text>
                  <Text
                    variant="titleMedium"
                    style={[styles.statValue, { color: colors.expense }]}
                  >
                    {formatAmount(monthlyStats.expenses)}
                  </Text>
                </View>
                <View style={styles.statItem}>
                  <MaterialIcons
                    name="account-balance"
                    size={24}
                    color={
                      monthlyStats.balance >= 0 ? colors.income : colors.expense
                    }
                  />
                  <Text variant="bodySmall" style={styles.statLabel}>
                    Balance
                  </Text>
                  <Text
                    variant="titleMedium"
                    style={[
                      styles.statValue,
                      {
                        color:
                          monthlyStats.balance >= 0
                            ? colors.income
                            : colors.expense,
                      },
                    ]}
                  >
                    {formatAmount(monthlyStats.balance)}
                  </Text>
                </View>
              </View>
            </Card.Content>
          </Card>

          <SegmentedButtons
            value={selectedType}
            onValueChange={(value) =>
              setSelectedType(value as "income" | "expense")
            }
            buttons={[
              { value: "expense", label: "Expenses" },
              { value: "income", label: "Income" },
            ]}
            style={styles.segmentedButtons}
          />

          {/* Trends Chart */}
          <Card style={styles.card} mode="outlined">
            <Card.Content>
              <Text variant="titleMedium" style={styles.cardTitle}>
                6-Month Trend
              </Text>

              {lineData.labels.length > 0 && (
                <LineChart
                  data={lineData}
                  width={screenWidth - 64}
                  height={220}
                  chartConfig={chartConfig}
                  bezier
                  style={styles.chart}
                />
              )}
            </Card.Content>
          </Card>

          {/* Category Breakdown */}
          <Card style={styles.card} mode="outlined">
            <Card.Content>
              <Text variant="titleMedium" style={styles.cardTitle}>
                {selectedType === "income" ? "Income" : "Expense"} Categories
              </Text>
              {pieData.length > 0 ? (
                <PieChart
                  data={pieData}
                  width={screenWidth - 64}
                  height={220}
                  chartConfig={chartConfig}
                  accessor="amount"
                  backgroundColor="transparent"
                  paddingLeft="15"
                  style={styles.chart}
                />
              ) : (
                <View style={styles.emptyChart}>
                  <Text
                    variant="bodyMedium"
                    style={{ color: theme.colors.onSurfaceVariant }}
                  >
                    No {selectedType} data for this month
                  </Text>
                </View>
              )}
            </Card.Content>
          </Card>

          {/* Category List */}
          {categoryStats.length > 0 && (
            <Card style={styles.card} mode="outlined">
              <Card.Content>
                <Text variant="titleMedium" style={styles.cardTitle}>
                  Category Details
                </Text>
                {categoryStats.map((cat) => (
                  <View key={cat.category_id} style={styles.categoryItem}>
                    <View style={styles.categoryLeft}>
                      <View
                        style={[
                          styles.categoryIcon,
                          { backgroundColor: cat.category_color + "20" },
                        ]}
                      >
                        <MaterialIcons
                          name={cat.category_icon}
                          size={20}
                          color={cat.category_color}
                        />
                      </View>
                      <Text variant="bodyMedium" style={styles.categoryName}>
                        {cat.category_name}
                      </Text>
                    </View>
                    <Text
                      variant="bodyMedium"
                      style={[
                        styles.categoryAmount,
                        { color: theme.colors.onSurface },
                      ]}
                    >
                      {formatAmount(cat.total_amount)}
                    </Text>
                  </View>
                ))}
              </Card.Content>
            </Card>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  title: {
    fontWeight: "700",
    marginBottom: 16,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
    borderRadius: 8,
  },
  cardTitle: {
    fontWeight: "600",
    marginBottom: 16,
  },
  overviewStats: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
  },
  statLabel: {
    marginTop: 4,
    marginBottom: 2,
  },
  statValue: {
    fontWeight: "700",
  },
  segmentedButtons: {
    marginBottom: 16,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  emptyChart: {
    height: 220,
    justifyContent: "center",
    alignItems: "center",
  },
  categoryItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  categoryLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  categoryName: {
    fontWeight: "500",
  },
  categoryAmount: {
    fontWeight: "600",
  },
});
