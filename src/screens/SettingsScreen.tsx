import React, { useState } from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import {
  Text,
  Card,
  List,
  useTheme,
  Button,
  Snackbar,
} from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";
import { seedDemoData } from "../utils/demoData";
import { useDatabase } from "../context/DatabaseContext";

export default function SettingsScreen() {
  const theme = useTheme();
  const { refreshTransactions } = useDatabase();
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSeedDemoData = async () => {
    setIsLoading(true);
    try {
      await seedDemoData();
      await refreshTransactions();
      setSnackbarVisible(true);
    } catch (error) {
      console.error("Error seeding demo data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <Text
            variant="headlineSmall"
            style={[styles.title, { color: theme.colors.onSurface }]}
          >
            Settings
          </Text>

          <Card style={styles.card} mode="outlined">
            <Card.Content>
              <Text variant="titleMedium" style={styles.cardTitle}>
                App Information
              </Text>
              <List.Item
                title="Version"
                description="1.0.0"
                left={(props) => <List.Icon {...props} icon="information" />}
              />
              <List.Item
                title="Developer"
                description="Finance Tracker Team"
                left={(props) => <List.Icon {...props} icon="account" />}
              />
            </Card.Content>
          </Card>

          <Card style={styles.card} mode="outlined">
            <Card.Content>
              <Text variant="titleMedium" style={styles.cardTitle}>
                Features
              </Text>
              <List.Item
                title="Transaction Management"
                description="Add, edit, and delete transactions"
                left={(props) => (
                  <List.Icon
                    {...props}
                    icon="check-circle"
                    color={theme.colors.primary}
                  />
                )}
              />
              <List.Item
                title="Category Organization"
                description="Organize transactions by categories"
                left={(props) => (
                  <List.Icon
                    {...props}
                    icon="check-circle"
                    color={theme.colors.primary}
                  />
                )}
              />
              <List.Item
                title="Analytics & Charts"
                description="Visual insights into your spending"
                left={(props) => (
                  <List.Icon
                    {...props}
                    icon="check-circle"
                    color={theme.colors.primary}
                  />
                )}
              />
              <List.Item
                title="Monthly Reports"
                description="Track income and expenses monthly"
                left={(props) => (
                  <List.Icon
                    {...props}
                    icon="check-circle"
                    color={theme.colors.primary}
                  />
                )}
              />
            </Card.Content>
          </Card>

          <Card style={styles.card} mode="outlined">
            <Card.Content>
              <Text variant="titleMedium" style={styles.cardTitle}>
                About
              </Text>
              <Text
                variant="bodyMedium"
                style={[
                  styles.aboutText,
                  { color: theme.colors.onSurfaceVariant },
                ]}
              >
                Finance Tracker is a simple and elegant personal finance
                management app. Track your income and expenses, categorize
                transactions, and gain insights into your spending habits with
                beautiful charts and analytics.
              </Text>
              <Text
                variant="bodyMedium"
                style={[
                  styles.aboutText,
                  { color: theme.colors.onSurfaceVariant },
                ]}
              >
                All your data is stored locally on your device for maximum
                privacy and security.
              </Text>
            </Card.Content>
          </Card>

          <Card style={styles.card} mode="outlined">
            <Card.Content>
              <Text variant="titleMedium" style={styles.cardTitle}>
                Demo Data
              </Text>
              <Text
                variant="bodyMedium"
                style={[
                  styles.aboutText,
                  { color: theme.colors.onSurfaceVariant },
                ]}
              >
                Add sample transactions to test the app features and see how
                analytics work.
              </Text>
              <Button
                mode="contained"
                onPress={handleSeedDemoData}
                loading={isLoading}
                disabled={isLoading}
                style={styles.demoButton}
              >
                Add Demo Transactions
              </Button>
            </Card.Content>
          </Card>
        </View>
      </ScrollView>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        Demo data added successfully!
      </Snackbar>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  title: {
    fontWeight: "700",
    marginBottom: 16,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  cardTitle: {
    fontWeight: "600",
    marginBottom: 8,
  },
  aboutText: {
    lineHeight: 20,
    marginBottom: 12,
  },
  demoButton: {
    marginTop: 8,
  },
});
