import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, RefreshControl } from 'react-native';
import { FAB, Text, useTheme, Searchbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TransactionCard } from '../components/TransactionCard';
import { AddTransactionModal } from '../components/AddTransactionModal';
import { StatsCard } from '../components/StatsCard';
import { useDatabase } from '../context/DatabaseContext';
import { TransactionWithCategory } from '../types';
import { colors } from '../theme/theme';

export default function TransactionsScreen() {
  const theme = useTheme();
  const { transactions, isLoading, refreshTransactions, deleteTransaction, getMonthlyStats } = useDatabase();
  
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTransactions, setFilteredTransactions] = useState<TransactionWithCategory[]>([]);
  const [monthlyStats, setMonthlyStats] = useState({ income: 0, expenses: 0, balance: 0 });

  useEffect(() => {
    // Filter transactions based on search query
    const filtered = transactions.filter(transaction =>
      transaction.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      transaction.category_name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredTransactions(filtered);
  }, [transactions, searchQuery]);

  useEffect(() => {
    // Load current month stats
    const loadMonthlyStats = async () => {
      const now = new Date();
      try {
        const stats = await getMonthlyStats(now.getFullYear(), now.getMonth() + 1);
        setMonthlyStats(stats);
      } catch (error) {
        console.error('Error loading monthly stats:', error);
      }
    };

    loadMonthlyStats();
  }, [transactions, getMonthlyStats]);

  const handleDeleteTransaction = async (id: number) => {
    try {
      await deleteTransaction(id);
    } catch (error) {
      console.error('Error deleting transaction:', error);
    }
  };

  const renderTransaction = ({ item }: { item: TransactionWithCategory }) => (
    <TransactionCard
      transaction={item}
      onDelete={() => handleDeleteTransaction(item.id)}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.statsContainer}>
        <StatsCard
          title="Income"
          amount={monthlyStats.income}
          icon="trending-up"
          color={colors.income}
          subtitle="This month"
        />
        <StatsCard
          title="Expenses"
          amount={monthlyStats.expenses}
          icon="trending-down"
          color={colors.expense}
          subtitle="This month"
        />
      </View>
      
      <StatsCard
        title="Balance"
        amount={monthlyStats.balance}
        icon="account-balance"
        color={monthlyStats.balance >= 0 ? colors.income : colors.expense}
        subtitle="Current month"
      />

      <Searchbar
        placeholder="Search transactions..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchbar}
      />

      <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        Recent Transactions
      </Text>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text variant="bodyLarge" style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
        No transactions found
      </Text>
      <Text variant="bodyMedium" style={[styles.emptySubtext, { color: theme.colors.onSurfaceVariant }]}>
        Tap the + button to add your first transaction
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={filteredTransactions}
        renderItem={renderTransaction}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refreshTransactions}
            colors={[theme.colors.primary]}
          />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => setModalVisible(true)}
      />

      <AddTransactionModal
        visible={modalVisible}
        onDismiss={() => setModalVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  searchbar: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 8,
  },
  listContent: {
    paddingBottom: 100,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '600',
  },
  emptySubtext: {
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});
