import React from "react";
import { View, StyleSheet } from "react-native";
import { Card, Text, IconButton, useTheme } from "react-native-paper";
import { MaterialIcons } from "@expo/vector-icons";
import { TransactionWithCategory } from "../types";
import { colors } from "../theme/theme";

interface TransactionCardProps {
  transaction: TransactionWithCategory;
  onEdit?: () => void;
  onDelete?: () => void;
}

export const TransactionCard: React.FC<TransactionCardProps> = ({
  transaction,
  onEdit,
  onDelete,
}) => {
  const theme = useTheme();
  const isIncome = transaction.type === "income";
  const amountColor = isIncome ? colors.income : colors.expense;
  const amountPrefix = isIncome ? "+" : "-";

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <Card style={styles.card} mode="outlined">
      <Card.Content>
        <View style={styles.container}>
          <View style={styles.leftSection}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: transaction.category_color + "20" },
              ]}
            >
              <MaterialIcons
                name={
                  transaction.category_icon as keyof typeof MaterialIcons.glyphMap
                }
                size={24}
                color={transaction.category_color}
              />
            </View>
            <View style={styles.details}>
              <Text variant="titleMedium" style={styles.description}>
                {transaction.description}
              </Text>
              <Text
                variant="bodySmall"
                style={[
                  styles.category,
                  { color: theme.colors.onSurfaceVariant },
                ]}
              >
                {transaction.category_name} • {formatDate(transaction.date)}
              </Text>
            </View>
          </View>

          <View style={styles.rightSection}>
            <Text
              variant="titleMedium"
              style={[styles.amount, { color: amountColor }]}
            >
              {amountPrefix}
              {formatAmount(transaction.amount)}
            </Text>
            <View style={styles.actions}>
              {onEdit && (
                <IconButton
                  icon="edit"
                  size={16}
                  onPress={onEdit}
                  style={styles.actionButton}
                />
              )}
              {onDelete && (
                <IconButton
                  icon="delete"
                  size={16}
                  onPress={onDelete}
                  style={styles.actionButton}
                />
              )}
            </View>
          </View>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginVertical: 4,
    marginHorizontal: 16,
    elevation: 2,
  },
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  leftSection: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  details: {
    flex: 1,
  },
  description: {
    fontWeight: "600",
    marginBottom: 2,
  },
  category: {
    fontSize: 12,
  },
  rightSection: {
    alignItems: "flex-end",
  },
  amount: {
    fontWeight: "700",
    marginBottom: 4,
  },
  actions: {
    flexDirection: "row",
  },
  actionButton: {
    margin: 0,
    padding: 4,
  },
});
