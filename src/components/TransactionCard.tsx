import React from "react";
import { View, StyleSheet } from "react-native";
import { Card, Text, IconButton, useTheme } from "react-native-paper";
import { MaterialIcons } from "@expo/vector-icons";
import { TransactionWithCategory } from "../types";
import { colors } from "../theme/theme";

interface TransactionCardProps {
  transaction: TransactionWithCategory;
  onEdit?: () => void;
  onDelete?: () => void;
}

export const TransactionCard: React.FC<TransactionCardProps> = ({
  transaction,
  onEdit,
  onDelete,
}) => {
  const theme = useTheme();
  const isIncome = transaction.type === "income";
  const amountColor = isIncome ? colors.income : colors.expense;
  const amountPrefix = isIncome ? "+" : "-";

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <Card
      style={[
        styles.card,
        { borderLeftWidth: 4, borderLeftColor: transaction.category_color },
      ]}
      mode="elevated"
    >
      <Card.Content style={styles.cardContent}>
        <View style={styles.container}>
          <View style={styles.leftSection}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: transaction.category_color + "15" },
              ]}
            >
              <MaterialIcons
                name={
                  transaction.category_icon as keyof typeof MaterialIcons.glyphMap
                }
                size={26}
                color={transaction.category_color}
              />
            </View>
            <View style={styles.details}>
              <Text variant="titleMedium" style={styles.description}>
                {transaction.description}
              </Text>
              <Text
                variant="bodySmall"
                style={[
                  styles.category,
                  { color: theme.colors.onSurfaceVariant },
                ]}
              >
                {transaction.category_name} • {formatDate(transaction.date)}
              </Text>
            </View>
          </View>

          <View style={styles.rightSection}>
            <Text
              variant="titleLarge"
              style={[styles.amount, { color: amountColor }]}
            >
              {amountPrefix}
              {formatAmount(transaction.amount)}
            </Text>
            <View style={styles.actions}>
              {onEdit && (
                <IconButton
                  icon="edit"
                  size={18}
                  onPress={onEdit}
                  style={styles.actionButton}
                  iconColor={theme.colors.onSurfaceVariant}
                />
              )}
              {onDelete && (
                <IconButton
                  icon="delete"
                  size={18}
                  onPress={onDelete}
                  style={styles.actionButton}
                  iconColor={colors.expense}
                />
              )}
            </View>
          </View>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginVertical: 6,
    marginHorizontal: 16,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    borderRadius: 8,
  },
  cardContent: {
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  leftSection: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconContainer: {
    width: 52,
    height: 52,
    borderRadius: 26,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  details: {
    flex: 1,
  },
  description: {
    fontWeight: "600",
    marginBottom: 4,
    fontSize: 16,
  },
  category: {
    fontSize: 13,
    opacity: 0.8,
  },
  rightSection: {
    alignItems: "flex-end",
  },
  amount: {
    fontWeight: "700",
    marginBottom: 6,
    fontSize: 18,
  },
  actions: {
    flexDirection: "row",
    marginTop: 4,
  },
  actionButton: {
    margin: 0,
    padding: 6,
  },
});
