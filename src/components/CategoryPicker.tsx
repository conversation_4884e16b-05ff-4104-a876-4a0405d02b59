import React, { useState, useEffect } from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import { Text, Chip, useTheme, ActivityIndicator } from "react-native-paper";
import { MaterialIcons } from "@expo/vector-icons";
import { Category } from "../types";
import { useDatabase } from "../context/DatabaseContext";

interface CategoryPickerProps {
  selectedCategoryId?: number;
  transactionType: "income" | "expense";
  onCategorySelect: (category: Category) => void;
}

export const CategoryPicker: React.FC<CategoryPickerProps> = ({
  selectedCategoryId,
  transactionType,
  onCategorySelect,
}) => {
  const theme = useTheme();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Safely get database context
  let getCategories:
    | ((type?: "income" | "expense") => Promise<Category[]>)
    | null = null;
  try {
    const dbContext = useDatabase();
    getCategories = dbContext.getCategories;
  } catch (error) {
    // Database context not available yet, will show fallback message
  }

  useEffect(() => {
    const loadCategories = async () => {
      if (!getCategories) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        console.log("Loading categories for type:", transactionType);
        const data = await getCategories(transactionType);
        console.log("Loading categories for type2:", transactionType);
        setCategories(data);
      } catch (error) {
        console.error("Error loading categories:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCategories();
  }, [transactionType, getCategories]);

  return (
    <View style={styles.container}>
      <Text
        variant="titleMedium"
        style={[styles.title, { color: theme.colors.onSurface }]}
      >
        Select Category
      </Text>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" />
        </View>
      ) : categories.length > 0 ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {categories.map((category) => (
            <Chip
              key={category.id}
              selected={selectedCategoryId === category.id}
              onPress={() => onCategorySelect(category)}
              style={[
                styles.chip,
                selectedCategoryId === category.id && {
                  backgroundColor: category.color + "20",
                  borderColor: category.color,
                  borderWidth: 1,
                },
              ]}
              textStyle={[
                styles.chipText,
                selectedCategoryId === category.id && { color: category.color },
              ]}
              icon={() => (
                <MaterialIcons
                  name={category.icon as keyof typeof MaterialIcons.glyphMap}
                  size={16}
                  color={
                    selectedCategoryId === category.id
                      ? category.color
                      : theme.colors.onSurfaceVariant
                  }
                />
              )}
            >
              {category.name}
            </Chip>
          ))}
        </ScrollView>
      ) : (
        <View style={styles.loadingContainer}>
          <Text
            variant="bodyMedium"
            style={{ color: theme.colors.onSurfaceVariant }}
          >
            Categories will load once the app is ready
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  title: {
    marginBottom: 16,
    marginHorizontal: 16,
    fontWeight: "700",
    fontSize: 18,
  },
  loadingContainer: {
    paddingVertical: 24,
    alignItems: "center",
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  chip: {
    marginRight: 12,
    marginVertical: 4,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  chipText: {
    fontSize: 13,
    fontWeight: "600",
  },
});
