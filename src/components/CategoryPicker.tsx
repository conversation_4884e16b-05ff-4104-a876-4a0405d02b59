import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Chip, useTheme } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { Category } from '../types';
import { useDatabase } from '../context/DatabaseContext';

interface CategoryPickerProps {
  selectedCategoryId?: number;
  transactionType: 'income' | 'expense';
  onCategorySelect: (category: Category) => void;
}

export const CategoryPicker: React.FC<CategoryPickerProps> = ({
  selectedCategoryId,
  transactionType,
  onCategorySelect,
}) => {
  const theme = useTheme();
  const { getCategories } = useDatabase();
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const data = await getCategories(transactionType);
        setCategories(data);
      } catch (error) {
        console.error('Error loading categories:', error);
      }
    };

    loadCategories();
  }, [transactionType, getCategories]);

  return (
    <View style={styles.container}>
      <Text variant="titleMedium" style={[styles.title, { color: theme.colors.onSurface }]}>
        Select Category
      </Text>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {categories.map((category) => (
          <Chip
            key={category.id}
            selected={selectedCategoryId === category.id}
            onPress={() => onCategorySelect(category)}
            style={[
              styles.chip,
              selectedCategoryId === category.id && {
                backgroundColor: category.color + '20',
                borderColor: category.color,
                borderWidth: 1,
              }
            ]}
            textStyle={[
              styles.chipText,
              selectedCategoryId === category.id && { color: category.color }
            ]}
            icon={() => (
              <MaterialIcons
                name={category.icon as keyof typeof MaterialIcons.glyphMap}
                size={16}
                color={selectedCategoryId === category.id ? category.color : theme.colors.onSurfaceVariant}
              />
            )}
          >
            {category.name}
          </Chip>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  title: {
    marginBottom: 12,
    marginHorizontal: 16,
    fontWeight: '600',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  chip: {
    marginRight: 8,
    marginVertical: 2,
  },
  chipText: {
    fontSize: 12,
    fontWeight: '500',
  },
});
