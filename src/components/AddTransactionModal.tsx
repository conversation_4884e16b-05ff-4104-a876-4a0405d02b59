import React, { useState } from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import {
  Modal,
  Portal,
  Text,
  TextInput,
  Button,
  SegmentedButtons,
  useTheme,
} from "react-native-paper";
import { CategoryPicker } from "./CategoryPicker";
import { Category } from "../types";
import { useDatabase } from "../context/DatabaseContext";

interface AddTransactionModalProps {
  visible: boolean;
  onDismiss: () => void;
}

export const AddTransactionModal: React.FC<AddTransactionModalProps> = ({
  visible,
  onDismiss,
}) => {
  const theme = useTheme();
  const { addTransaction } = useDatabase();

  const [amount, setAmount] = useState("");
  const [description, setDescription] = useState("");
  const [type, setType] = useState<"income" | "expense">("expense");
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null
  );
  const [date, setDate] = useState(new Date().toISOString().split("T")[0]);
  const [isLoading, setIsLoading] = useState(false);

  const resetForm = () => {
    setAmount("");
    setDescription("");
    setType("expense");
    setSelectedCategory(null);
    setDate(new Date().toISOString().split("T")[0]);
  };

  const handleSubmit = async () => {
    if (!amount || !description || !selectedCategory) {
      return;
    }

    setIsLoading(true);
    try {
      await addTransaction({
        amount: parseFloat(amount),
        description,
        category_id: selectedCategory.id,
        type,
        date,
      });

      resetForm();
      onDismiss();
    } catch (error) {
      console.error("Error adding transaction:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    resetForm();
    onDismiss();
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={handleCancel}
        contentContainerStyle={[
          styles.modal,
          { backgroundColor: theme.colors.surface },
        ]}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text
            variant="headlineSmall"
            style={[styles.title, { color: theme.colors.onSurface }]}
          >
            Add Transaction
          </Text>

          <SegmentedButtons
            value={type}
            onValueChange={(value) => {
              setType(value as "income" | "expense");
              setSelectedCategory(null); // Reset category when type changes
            }}
            buttons={[
              {
                value: "expense",
                label: "Expense",
                icon: "trending-down",
              },
              {
                value: "income",
                label: "Income",
                icon: "trending-up",
              },
            ]}
            style={styles.segmentedButtons}
          />

          <TextInput
            label="Amount"
            value={amount}
            onChangeText={setAmount}
            keyboardType="numeric"
            mode="outlined"
            style={styles.input}
            left={<TextInput.Icon icon="currency-usd" />}
          />

          <TextInput
            label="Description"
            value={description}
            onChangeText={setDescription}
            mode="outlined"
            style={styles.input}
            left={<TextInput.Icon icon="text" />}
          />

          <TextInput
            label="Date"
            value={date}
            onChangeText={setDate}
            mode="outlined"
            style={styles.input}
            left={<TextInput.Icon icon="calendar" />}
            placeholder="YYYY-MM-DD"
          />

          <CategoryPicker
            selectedCategoryId={selectedCategory?.id}
            transactionType={type}
            onCategorySelect={setSelectedCategory}
          />

          <View style={styles.actions}>
            <Button
              mode="outlined"
              onPress={handleCancel}
              style={styles.button}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSubmit}
              loading={isLoading}
              disabled={
                !amount || !description || !selectedCategory || isLoading
              }
              style={styles.button}
            >
              Add Transaction
            </Button>
          </View>
        </ScrollView>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 20,
    borderRadius: 8,
    padding: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  title: {
    textAlign: "center",
    marginBottom: 24,
    fontWeight: "800",
    fontSize: 24,
  },
  segmentedButtons: {
    marginBottom: 20,
  },
  input: {
    marginBottom: 18,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 24,
    gap: 16,
  },
  button: {
    flex: 1,
    borderRadius: 12,
  },
});
