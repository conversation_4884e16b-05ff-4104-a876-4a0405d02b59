import * as SQLite from 'expo-sqlite';
import { Transaction, Category, TransactionWithCategory } from '../types';

const DATABASE_NAME = 'finance_tracker.db';

export class Database {
  private db: SQLite.SQLiteDatabase | null = null;
  private initialized = false;

  async initialize() {
    if (this.initialized) return;

    this.db = await SQLite.openDatabaseAsync(DATABASE_NAME);
    await this.initializeTables();
    this.initialized = true;
  }

  private async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  private async initializeTables() {
    if (!this.db) throw new Error('Database not initialized');

    // Create categories table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        icon TEXT NOT NULL,
        color TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('income', 'expense', 'both')),
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create transactions table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        category_id INTEGER NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
        date TEXT NOT NULL,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories (id)
      );
    `);

    // Insert default categories
    await this.insertDefaultCategories();
  }

  private async insertDefaultCategories() {
    if (!this.db) throw new Error('Database not initialized');

    const defaultCategories = [
      // Income categories
      { name: 'Salary', icon: 'work', color: '#4CAF50', type: 'income' },
      { name: 'Freelance', icon: 'laptop', color: '#2196F3', type: 'income' },
      { name: 'Investment', icon: 'trending-up', color: '#FF9800', type: 'income' },
      { name: 'Gift', icon: 'card-giftcard', color: '#E91E63', type: 'income' },

      // Expense categories
      { name: 'Food & Dining', icon: 'restaurant', color: '#FF5722', type: 'expense' },
      { name: 'Transportation', icon: 'directions-car', color: '#607D8B', type: 'expense' },
      { name: 'Shopping', icon: 'shopping-cart', color: '#9C27B0', type: 'expense' },
      { name: 'Entertainment', icon: 'movie', color: '#3F51B5', type: 'expense' },
      { name: 'Bills & Utilities', icon: 'receipt', color: '#795548', type: 'expense' },
      { name: 'Healthcare', icon: 'local-hospital', color: '#F44336', type: 'expense' },
      { name: 'Education', icon: 'school', color: '#009688', type: 'expense' },
      { name: 'Travel', icon: 'flight', color: '#00BCD4', type: 'expense' },
    ];

    const insertStmt = await this.db.prepareAsync(
      'INSERT OR IGNORE INTO categories (name, icon, color, type) VALUES (?, ?, ?, ?)'
    );

    for (const category of defaultCategories) {
      await insertStmt.executeAsync([category.name, category.icon, category.color, category.type]);
    }

    await insertStmt.finalizeAsync();
  }

  // Transaction methods
  async addTransaction(transaction: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const stmt = await this.db.prepareAsync(
      'INSERT INTO transactions (amount, description, category_id, type, date) VALUES (?, ?, ?, ?, ?)'
    );
    const result = await stmt.executeAsync([
      transaction.amount,
      transaction.description,
      transaction.category_id,
      transaction.type,
      transaction.date
    ]);
    await stmt.finalizeAsync();
    return result.lastInsertRowId;
  }

  async getTransactions(limit?: number): Promise<TransactionWithCategory[]> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const query = `
      SELECT
        t.*,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      ORDER BY t.date DESC, t.created_at DESC
      ${limit ? `LIMIT ${limit}` : ''}
    `;

    const stmt = await this.db.prepareAsync(query);
    const result = await stmt.executeAsync();
    const transactions: TransactionWithCategory[] = [];

    for await (const row of result) {
      transactions.push(row as TransactionWithCategory);
    }

    await stmt.finalizeAsync();
    return transactions;
  }

  async updateTransaction(id: number, transaction: Partial<Omit<Transaction, 'id' | 'created_at'>>): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const fields = Object.keys(transaction).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(transaction), new Date().toISOString(), id];

    const stmt = await this.db.prepareAsync(
      `UPDATE transactions SET ${fields}, updated_at = ? WHERE id = ?`
    );
    await stmt.executeAsync(values);
    await stmt.finalizeAsync();
  }

  async deleteTransaction(id: number): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const stmt = await this.db.prepareAsync('DELETE FROM transactions WHERE id = ?');
    await stmt.executeAsync([id]);
    await stmt.finalizeAsync();
  }

  // Category methods
  async getCategories(type?: 'income' | 'expense'): Promise<Category[]> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const query = type
      ? 'SELECT * FROM categories WHERE type = ? OR type = "both" ORDER BY name'
      : 'SELECT * FROM categories ORDER BY name';
    const params = type ? [type] : [];

    const stmt = await this.db.prepareAsync(query);
    const result = await stmt.executeAsync(params);
    const categories: Category[] = [];

    for await (const row of result) {
      categories.push(row as Category);
    }

    await stmt.finalizeAsync();
    return categories;
  }

  // Analytics methods
  async getMonthlyStats(year: number, month: number): Promise<{ income: number; expenses: number; balance: number }> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
    const endDate = `${year}-${month.toString().padStart(2, '0')}-31`;

    const stmt = await this.db.prepareAsync(`
      SELECT
        type,
        SUM(amount) as total
      FROM transactions
      WHERE date >= ? AND date <= ?
      GROUP BY type
    `);

    const result = await stmt.executeAsync([startDate, endDate]);
    let income = 0;
    let expenses = 0;

    for await (const row of result) {
      const data = row as { type: string; total: number };
      if (data.type === 'income') {
        income = data.total;
      } else if (data.type === 'expense') {
        expenses = data.total;
      }
    }

    await stmt.finalizeAsync();
    return {
      income,
      expenses,
      balance: income - expenses
    };
  }

  async getCategoryStats(year: number, month: number, type: 'income' | 'expense'): Promise<any[]> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
    const endDate = `${year}-${month.toString().padStart(2, '0')}-31`;

    const stmt = await this.db.prepareAsync(`
      SELECT
        c.id as category_id,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color,
        SUM(t.amount) as total_amount,
        COUNT(t.id) as transaction_count
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      WHERE t.date >= ? AND t.date <= ? AND t.type = ?
      GROUP BY c.id, c.name, c.icon, c.color
      ORDER BY total_amount DESC
    `);

    const result = await stmt.executeAsync([startDate, endDate, type]);
    const stats = [];

    for await (const row of result) {
      stats.push(row);
    }

    await stmt.finalizeAsync();
    return stats;
  }
}

export const database = new Database();
