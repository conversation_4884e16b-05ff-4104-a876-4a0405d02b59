import * as SQLite from 'expo-sqlite';
import { Transaction, Category, TransactionWithCategory } from '../types';

const DATABASE_NAME = 'finance_tracker.db';

export class Database {
  private db: SQLite.SQLiteDatabase;

  constructor() {
    this.db = SQLite.openDatabase(DATABASE_NAME);
    this.initializeTables();
  }

  private initializeTables() {
    this.db.transaction((tx) => {
      // Create categories table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          icon TEXT NOT NULL,
          color TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('income', 'expense', 'both')),
          created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Create transactions table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          amount REAL NOT NULL,
          description TEXT NOT NULL,
          category_id INTEGER NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
          date TEXT NOT NULL,
          created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (category_id) REFERENCES categories (id)
        );
      `);

      // Insert default categories
      this.insertDefaultCategories(tx);
    });
  }

  private insertDefaultCategories(tx: SQLite.SQLTransaction) {
    const defaultCategories = [
      // Income categories
      { name: 'Salary', icon: 'work', color: '#4CAF50', type: 'income' },
      { name: 'Freelance', icon: 'laptop', color: '#2196F3', type: 'income' },
      { name: 'Investment', icon: 'trending-up', color: '#FF9800', type: 'income' },
      { name: 'Gift', icon: 'card-giftcard', color: '#E91E63', type: 'income' },
      
      // Expense categories
      { name: 'Food & Dining', icon: 'restaurant', color: '#FF5722', type: 'expense' },
      { name: 'Transportation', icon: 'directions-car', color: '#607D8B', type: 'expense' },
      { name: 'Shopping', icon: 'shopping-cart', color: '#9C27B0', type: 'expense' },
      { name: 'Entertainment', icon: 'movie', color: '#3F51B5', type: 'expense' },
      { name: 'Bills & Utilities', icon: 'receipt', color: '#795548', type: 'expense' },
      { name: 'Healthcare', icon: 'local-hospital', color: '#F44336', type: 'expense' },
      { name: 'Education', icon: 'school', color: '#009688', type: 'expense' },
      { name: 'Travel', icon: 'flight', color: '#00BCD4', type: 'expense' },
    ];

    defaultCategories.forEach((category) => {
      tx.executeSql(
        'INSERT OR IGNORE INTO categories (name, icon, color, type) VALUES (?, ?, ?, ?)',
        [category.name, category.icon, category.color, category.type]
      );
    });
  }

  // Transaction methods
  addTransaction(transaction: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    return new Promise((resolve, reject) => {
      this.db.transaction((tx) => {
        tx.executeSql(
          'INSERT INTO transactions (amount, description, category_id, type, date) VALUES (?, ?, ?, ?, ?)',
          [transaction.amount, transaction.description, transaction.category_id, transaction.type, transaction.date],
          (_, result) => resolve(result.insertId!),
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }

  getTransactions(limit?: number): Promise<TransactionWithCategory[]> {
    return new Promise((resolve, reject) => {
      this.db.transaction((tx) => {
        const query = `
          SELECT 
            t.*,
            c.name as category_name,
            c.icon as category_icon,
            c.color as category_color
          FROM transactions t
          JOIN categories c ON t.category_id = c.id
          ORDER BY t.date DESC, t.created_at DESC
          ${limit ? `LIMIT ${limit}` : ''}
        `;
        
        tx.executeSql(
          query,
          [],
          (_, result) => {
            const transactions: TransactionWithCategory[] = [];
            for (let i = 0; i < result.rows.length; i++) {
              transactions.push(result.rows.item(i));
            }
            resolve(transactions);
          },
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }

  updateTransaction(id: number, transaction: Partial<Omit<Transaction, 'id' | 'created_at'>>): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db.transaction((tx) => {
        const fields = Object.keys(transaction).map(key => `${key} = ?`).join(', ');
        const values = [...Object.values(transaction), new Date().toISOString(), id];
        
        tx.executeSql(
          `UPDATE transactions SET ${fields}, updated_at = ? WHERE id = ?`,
          values,
          () => resolve(),
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }

  deleteTransaction(id: number): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db.transaction((tx) => {
        tx.executeSql(
          'DELETE FROM transactions WHERE id = ?',
          [id],
          () => resolve(),
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }

  // Category methods
  getCategories(type?: 'income' | 'expense'): Promise<Category[]> {
    return new Promise((resolve, reject) => {
      this.db.transaction((tx) => {
        const query = type 
          ? 'SELECT * FROM categories WHERE type = ? OR type = "both" ORDER BY name'
          : 'SELECT * FROM categories ORDER BY name';
        const params = type ? [type] : [];
        
        tx.executeSql(
          query,
          params,
          (_, result) => {
            const categories: Category[] = [];
            for (let i = 0; i < result.rows.length; i++) {
              categories.push(result.rows.item(i));
            }
            resolve(categories);
          },
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }

  // Analytics methods
  getMonthlyStats(year: number, month: number): Promise<{ income: number; expenses: number; balance: number }> {
    return new Promise((resolve, reject) => {
      this.db.transaction((tx) => {
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
        const endDate = `${year}-${month.toString().padStart(2, '0')}-31`;
        
        tx.executeSql(
          `SELECT 
            type,
            SUM(amount) as total
          FROM transactions 
          WHERE date >= ? AND date <= ?
          GROUP BY type`,
          [startDate, endDate],
          (_, result) => {
            let income = 0;
            let expenses = 0;
            
            for (let i = 0; i < result.rows.length; i++) {
              const row = result.rows.item(i);
              if (row.type === 'income') {
                income = row.total;
              } else if (row.type === 'expense') {
                expenses = row.total;
              }
            }
            
            resolve({
              income,
              expenses,
              balance: income - expenses
            });
          },
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }

  getCategoryStats(year: number, month: number, type: 'income' | 'expense'): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this.db.transaction((tx) => {
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
        const endDate = `${year}-${month.toString().padStart(2, '0')}-31`;
        
        tx.executeSql(
          `SELECT 
            c.id as category_id,
            c.name as category_name,
            c.icon as category_icon,
            c.color as category_color,
            SUM(t.amount) as total_amount,
            COUNT(t.id) as transaction_count
          FROM transactions t
          JOIN categories c ON t.category_id = c.id
          WHERE t.date >= ? AND t.date <= ? AND t.type = ?
          GROUP BY c.id, c.name, c.icon, c.color
          ORDER BY total_amount DESC`,
          [startDate, endDate, type],
          (_, result) => {
            const stats = [];
            for (let i = 0; i < result.rows.length; i++) {
              stats.push(result.rows.item(i));
            }
            resolve(stats);
          },
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }
}

export const database = new Database();
